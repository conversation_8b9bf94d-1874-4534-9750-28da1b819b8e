"""
智能数据分析平台 - 主应用程序
"""
import streamlit as st
import pandas as pd
import os
from typing import Optional

# 导入自定义模块
from config import Config
from core.data_manager import DataManager
from core.llm_manager import LLMManager
from core.analysis_engine import AnalysisEngine
from utils.logger import setup_logger, get_logger

# 设置页面配置
st.set_page_config(
    page_title=Config.APP_TITLE,
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 初始化日志
setup_logger()
logger = get_logger(__name__)

class DataAnalysisApp:
    """数据分析应用主类"""
    
    def __init__(self):
        self.data_manager = DataManager()
        self.llm_manager = LLMManager()
        self.analysis_engine = AnalysisEngine()
        
        # 初始化会话状态
        self._init_session_state()
    
    def _init_session_state(self):
        """初始化会话状态"""
        if 'messages' not in st.session_state:
            st.session_state.messages = []
        
        if 'current_file' not in st.session_state:
            st.session_state.current_file = None
        
        if 'data_loaded' not in st.session_state:
            st.session_state.data_loaded = False
    
    def render_sidebar(self):
        """渲染侧边栏"""
        st.sidebar.title("📁 文件管理")
        
        # 文件上传
        uploaded_file = st.sidebar.file_uploader(
            "上传数据文件",
            type=Config.SUPPORTED_FILE_TYPES,
            help=f"支持的格式: {', '.join(Config.SUPPORTED_FILE_TYPES)}"
        )
        
        if uploaded_file is not None:
            # 保存上传的文件
            if self.data_manager.save_uploaded_file(uploaded_file.getvalue(), uploaded_file.name):
                st.sidebar.success(f"✅ 文件 '{uploaded_file.name}' 已保存")
                st.rerun()
        
        # 显示已上传的文件列表
        uploaded_files = self.data_manager.get_uploaded_files()
        
        if uploaded_files:
            st.sidebar.subheader("已上传的文件")
            selected_file = st.sidebar.selectbox(
                "选择要分析的文件",
                options=["请选择文件..."] + uploaded_files,
                index=0 if st.session_state.current_file is None else 
                      (uploaded_files.index(st.session_state.current_file) + 1 
                       if st.session_state.current_file in uploaded_files else 0)
            )
            
            if selected_file != "请选择文件..." and selected_file != st.session_state.current_file:
                self._load_file(selected_file)
        else:
            st.sidebar.info("暂无已上传的文件")
    
    def _load_file(self, filename: str):
        """加载选中的文件"""
        try:
            df, error = self.data_manager.load_data(filename)
            
            if error:
                st.sidebar.error(f"❌ {error}")
                return
            
            # 加载数据到分析引擎
            if self.analysis_engine.load_data(df):
                st.session_state.current_file = filename
                st.session_state.data_loaded = True
                
                # 清空之前的对话历史
                st.session_state.messages = []
                
                # 添加欢迎消息
                welcome_msg = f"✅ 已成功加载文件 '{filename}'\n\n数据概览:\n- 形状: {df.shape[0]} 行 × {df.shape[1]} 列\n- 列名: {', '.join(df.columns.tolist())}\n\n您可以开始提问了！"
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": welcome_msg
                })
                
                st.sidebar.success(f"✅ 文件加载成功")
                logger.info(f"文件加载成功: {filename}")
            else:
                st.sidebar.error("❌ 数据加载到分析引擎失败")
                
        except Exception as e:
            error_msg = f"加载文件时出错: {str(e)}"
            st.sidebar.error(f"❌ {error_msg}")
            logger.error(error_msg)
    
    def render_main_content(self):
        """渲染主内容区域"""
        st.title(Config.APP_TITLE)
        
        if not st.session_state.data_loaded:
            st.info("👈 请先在左侧上传并选择一个数据文件开始分析")
            return
        
        # 显示对话历史
        for message in st.session_state.messages:
            with st.chat_message(message["role"]):
                self._render_message_content(message["content"])
        
        # 用户输入
        if prompt := st.chat_input("请输入您的问题..."):
            self._handle_user_input(prompt)
    
    def _render_message_content(self, content):
        """渲染消息内容"""
        if isinstance(content, str):
            st.markdown(content)
        elif isinstance(content, dict):
            if content.get('type') == 'dataframe':
                st.dataframe(content['content'])
            elif content.get('type') == 'plot':
                st.pyplot(content['content'])
            elif content.get('type') == 'text':
                st.markdown(content['content'])
            elif content.get('type') == 'error':
                st.error(content['content'])
        else:
            st.write(content)
    
    def _handle_user_input(self, user_input: str):
        """处理用户输入"""
        # 添加用户消息到历史
        st.session_state.messages.append({
            "role": "user",
            "content": user_input
        })
        
        # 显示用户消息
        with st.chat_message("user"):
            st.markdown(user_input)
        
        # 生成助手回复
        with st.chat_message("assistant"):
            with st.spinner("正在分析中..."):
                response = self._generate_response(user_input)
                self._render_message_content(response)
        
        # 添加助手回复到历史
        st.session_state.messages.append({
            "role": "assistant",
            "content": response
        })
    
    def _generate_response(self, user_input: str) -> dict:
        """生成回复"""
        try:
            # 获取当前数据的列名用于意图判断
            if hasattr(self.analysis_engine, 'current_data') and self.analysis_engine.current_data is not None:
                columns = self.analysis_engine.current_data.columns.tolist()
            else:
                columns = []
            
            # 意图判断
            if not self.llm_manager.judge_intent(user_input, columns):
                return {
                    'type': 'text',
                    'content': self.llm_manager.get_guidance_response()
                }
            
            # 执行数据分析
            result = self.analysis_engine.analyze(user_input)
            return result
            
        except Exception as e:
            error_msg = f"处理请求时出错: {str(e)}"
            logger.error(error_msg)
            return {
                'type': 'error',
                'content': error_msg
            }
    
    def run(self):
        """运行应用"""
        try:
            # 验证配置
            Config.validate_config()
            
            # 渲染界面
            self.render_sidebar()
            self.render_main_content()
            
        except Exception as e:
            st.error(f"应用启动失败: {str(e)}")
            logger.error(f"应用启动失败: {e}")

def main():
    """主函数"""
    app = DataAnalysisApp()
    app.run()

if __name__ == "__main__":
    main()
