"""
数据分析引擎模块
"""
import pandas as pd
from typing import Any, Optional, Union
import matplotlib.pyplot as plt
import matplotlib
import io
import base64
from pandasai import SmartDataframe
from pandasai.llm import OpenAI
from config import Config
from utils.logger import get_logger

# 设置matplotlib后端
matplotlib.use('Agg')
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

logger = get_logger(__name__)

class AnalysisEngine:
    """数据分析引擎"""
    
    def __init__(self):
        self.smart_df = None
        self.current_data = None
        self._setup_llm()
    
    def _setup_llm(self):
        """设置LLM配置"""
        try:
            # 使用OpenAI兼容的配置来连接qwen-max
            self.llm = OpenAI(
                api_token=Config.QWEN_API_KEY,
                base_url=Config.QWEN_BASE_URL,
                model="qwen-max"
            )
            logger.info("LLM配置成功")
        except Exception as e:
            logger.error(f"LLM配置失败: {e}")
            self.llm = None
    
    def load_data(self, df: pd.DataFrame) -> bool:
        """加载数据到分析引擎"""
        try:
            self.current_data = df.copy()
            self.smart_df = SmartDataframe(df, config={"llm": self.llm})
            logger.info(f"数据加载到分析引擎成功，形状: {df.shape}")
            return True
        except Exception as e:
            logger.error(f"数据加载到分析引擎失败: {e}")
            return False
    
    def analyze(self, question: str) -> dict:
        """
        执行数据分析
        
        Args:
            question: 用户问题
            
        Returns:
            dict: 包含结果类型和内容的字典
        """
        if not self.smart_df:
            return {
                'type': 'error',
                'content': '请先加载数据文件'
            }
        
        try:
            logger.info(f"开始分析问题: {question}")
            
            # 调用PandasAI进行分析
            result = self.smart_df.chat(question)
            
            # 判断结果类型并格式化
            return self._format_result(result)
            
        except Exception as e:
            error_msg = f"分析过程中出现错误: {str(e)}"
            logger.error(error_msg)
            return {
                'type': 'error',
                'content': error_msg
            }
    
    def _format_result(self, result: Any) -> dict:
        """格式化分析结果"""
        try:
            # 字符串结果
            if isinstance(result, str):
                return {
                    'type': 'text',
                    'content': result
                }
            
            # DataFrame结果
            elif isinstance(result, pd.DataFrame):
                return {
                    'type': 'dataframe',
                    'content': result
                }
            
            # 数值结果
            elif isinstance(result, (int, float)):
                return {
                    'type': 'text',
                    'content': str(result)
                }
            
            # 图表结果（matplotlib figure）
            elif hasattr(result, 'savefig'):
                return {
                    'type': 'plot',
                    'content': result
                }
            
            # 其他类型转为字符串
            else:
                return {
                    'type': 'text',
                    'content': str(result)
                }
                
        except Exception as e:
            logger.error(f"结果格式化失败: {e}")
            return {
                'type': 'error',
                'content': f"结果格式化失败: {str(e)}"
            }
    
    def get_data_preview(self, n_rows: int = 5) -> dict:
        """获取数据预览"""
        if self.current_data is None:
            return {
                'type': 'error',
                'content': '没有加载的数据'
            }
        
        try:
            preview_df = self.current_data.head(n_rows)
            return {
                'type': 'dataframe',
                'content': preview_df
            }
        except Exception as e:
            return {
                'type': 'error',
                'content': f"获取数据预览失败: {str(e)}"
            }
    
    def get_data_summary(self) -> dict:
        """获取数据摘要信息"""
        if self.current_data is None:
            return {
                'type': 'error',
                'content': '没有加载的数据'
            }
        
        try:
            summary = []
            summary.append(f"数据形状: {self.current_data.shape[0]} 行 × {self.current_data.shape[1]} 列")
            summary.append(f"列名: {', '.join(self.current_data.columns.tolist())}")
            
            # 数据类型统计
            dtype_counts = self.current_data.dtypes.value_counts()
            summary.append("数据类型分布:")
            for dtype, count in dtype_counts.items():
                summary.append(f"  - {dtype}: {count} 列")
            
            # 缺失值统计
            null_counts = self.current_data.isnull().sum()
            if null_counts.sum() > 0:
                summary.append("缺失值情况:")
                for col, count in null_counts[null_counts > 0].items():
                    summary.append(f"  - {col}: {count} 个缺失值")
            else:
                summary.append("无缺失值")
            
            return {
                'type': 'text',
                'content': '\n'.join(summary)
            }
            
        except Exception as e:
            return {
                'type': 'error',
                'content': f"获取数据摘要失败: {str(e)}"
            }
