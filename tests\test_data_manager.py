"""
数据管理器测试
"""
import unittest
import tempfile
import os
import pandas as pd
from core.data_manager import DataManager

class TestDataManager(unittest.TestCase):
    """数据管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        # 临时修改上传目录
        self.original_upload_dir = DataManager().upload_dir
        DataManager().upload_dir = self.temp_dir
        self.data_manager = DataManager()
    
    def tearDown(self):
        """测试后清理"""
        # 恢复原始上传目录
        DataManager().upload_dir = self.original_upload_dir
        # 清理临时文件
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_save_uploaded_file(self):
        """测试文件保存功能"""
        test_content = b"test,data\n1,2\n3,4"
        filename = "test.csv"
        
        result = self.data_manager.save_uploaded_file(test_content, filename)
        self.assertTrue(result)
        
        # 验证文件是否存在
        file_path = os.path.join(self.temp_dir, filename)
        self.assertTrue(os.path.exists(file_path))
        
        # 验证文件内容
        with open(file_path, 'rb') as f:
            saved_content = f.read()
        self.assertEqual(saved_content, test_content)
    
    def test_get_uploaded_files(self):
        """测试获取文件列表功能"""
        # 创建测试文件
        test_files = ["test1.csv", "test2.xlsx", "test3.txt"]
        for filename in test_files:
            file_path = os.path.join(self.temp_dir, filename)
            with open(file_path, 'w') as f:
                f.write("test")
        
        files = self.data_manager.get_uploaded_files()
        
        # 应该只返回支持的文件类型
        expected_files = ["test1.csv", "test2.xlsx"]
        self.assertEqual(sorted(files), sorted(expected_files))
    
    def test_load_csv_data(self):
        """测试CSV文件加载"""
        # 创建测试CSV文件
        test_data = "name,age,city\nAlice,25,Beijing\nBob,30,Shanghai"
        filename = "test.csv"
        file_path = os.path.join(self.temp_dir, filename)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(test_data)
        
        df, error = self.data_manager.load_data(filename)
        
        self.assertIsNone(error)
        self.assertIsNotNone(df)
        self.assertEqual(df.shape, (2, 3))
        self.assertEqual(list(df.columns), ['name', 'age', 'city'])
    
    def test_load_nonexistent_file(self):
        """测试加载不存在的文件"""
        df, error = self.data_manager.load_data("nonexistent.csv")
        
        self.assertIsNone(df)
        self.assertIsNotNone(error)
        self.assertIn("文件不存在", error)
    
    def test_get_data_info(self):
        """测试获取数据信息"""
        # 创建测试数据
        df = pd.DataFrame({
            'A': [1, 2, None],
            'B': ['x', 'y', 'z'],
            'C': [1.1, 2.2, 3.3]
        })
        
        info = self.data_manager.get_data_info(df)
        
        self.assertEqual(info['shape'], (3, 3))
        self.assertEqual(info['columns'], ['A', 'B', 'C'])
        self.assertEqual(info['null_counts']['A'], 1)
        self.assertEqual(info['null_counts']['B'], 0)

if __name__ == '__main__':
    unittest.main()
