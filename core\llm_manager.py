"""
大语言模型管理模块
"""
import json
from typing import Optional, Dict, Any
from litellm import completion
from config import Config
from utils.logger import get_logger
from utils.error_handler import handle_exceptions, ErrorMessages

logger = get_logger(__name__)

class LLMManager:
    """大语言模型管理器"""
    
    def __init__(self):
        self.api_key = Config.QWEN_API_KEY
        self.base_url = Config.QWEN_BASE_URL
        self.model = "qwen-max"
        
    @handle_exceptions(default_return=None)
    def _make_api_call(self, messages: list, temperature: float = 0.1) -> Optional[str]:
        """调用LLM API"""
        if not self.api_key:
            logger.error("API密钥未配置")
            return None

        response = completion(
            model=self.model,
            messages=messages,
            api_key=self.api_key,
            base_url=self.base_url,
            temperature=temperature
        )
        return response.choices[0].message.content.strip()
    
    def judge_intent(self, user_question: str, data_columns: list) -> bool:
        """
        判断用户问题是否与数据分析相关
        
        Args:
            user_question: 用户问题
            data_columns: 数据列名列表
            
        Returns:
            bool: True表示相关，False表示不相关
        """
        columns_str = "、".join(data_columns)
        
        prompt = f"""
你是一个意图判断助手。请判断用户的问题是否与给定的数据集相关。

数据集包含以下列：{columns_str}

用户问题：{user_question}

判断规则：
1. 如果问题涉及数据查询、统计、分析、可视化等与数据相关的操作，回答"是"
2. 如果问题涉及数据集中的列名或可能的数据内容，回答"是"
3. 如果问题是关于聊天、闲谈、写作、编程（非数据分析）等与数据无关的内容，回答"否"

请只回答"是"或"否"，不要添加任何其他内容。
"""
        
        messages = [{"role": "user", "content": prompt}]
        response = self._make_api_call(messages)
        
        if response:
            logger.info(f"意图判断 - 问题: {user_question}, 结果: {response}")
            return response.strip() == "是"
        else:
            # API调用失败时，默认认为是相关的，避免误拦截
            logger.warning("意图判断API调用失败，默认认为问题相关")
            return True
    
    def get_guidance_response(self) -> str:
        """获取引导性回复"""
        return Config.GUIDANCE_MESSAGE
