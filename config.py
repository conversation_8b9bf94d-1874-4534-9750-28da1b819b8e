"""
配置文件 - 管理应用程序的所有配置项
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """应用配置类"""
    
    # API配置
    QWEN_API_KEY = os.getenv("QWEN_API_KEY", "")
    QWEN_BASE_URL = os.getenv("QWEN_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1")
    
    # 应用配置
    APP_TITLE = os.getenv("APP_TITLE", "智能数据分析平台")
    UPLOAD_DIRECTORY = os.getenv("UPLOAD_DIRECTORY", "uploaded_files")
    MAX_FILE_SIZE_MB = int(os.getenv("MAX_FILE_SIZE_MB", "100"))
    
    # 日志配置
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE = os.getenv("LOG_FILE", "app.log")
    
    # 支持的文件类型
    SUPPORTED_FILE_TYPES = ["csv", "xlsx", "xls"]
    
    # 引导话术
    GUIDANCE_MESSAGE = """
    我是一个数据分析助手，专门帮助您分析上传的数据文件。
    
    请提出与您上传数据相关的问题，例如：
    - "显示前10行数据"
    - "哪个产品的销售额最高？"
    - "按月份绘制销售趋势图"
    - "计算各类别的平均值"
    - "找出异常值"
    
    我可以帮您进行数据查询、统计分析、可视化等操作。
    """
    
    @classmethod
    def validate_config(cls):
        """验证配置是否完整"""
        if not cls.QWEN_API_KEY:
            raise ValueError("QWEN_API_KEY 未设置，请在.env文件中配置您的API密钥")
        
        # 创建上传目录
        if not os.path.exists(cls.UPLOAD_DIRECTORY):
            os.makedirs(cls.UPLOAD_DIRECTORY)
            
        return True
