#!/usr/bin/env python3
"""
应用测试脚本
"""
import os
import sys
import unittest
from pathlib import Path

def run_tests():
    """运行所有测试"""
    print("🧪 运行测试套件...")
    
    # 发现并运行测试
    loader = unittest.TestLoader()
    start_dir = 'tests'
    
    if not Path(start_dir).exists():
        print(f"❌ 测试目录 {start_dir} 不存在")
        return False
    
    suite = loader.discover(start_dir, pattern='test_*.py')
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    if result.wasSuccessful():
        print("✅ 所有测试通过")
        return True
    else:
        print("❌ 部分测试失败")
        return False

def check_imports():
    """检查关键模块导入"""
    print("📦 检查模块导入...")
    
    modules_to_check = [
        'streamlit',
        'pandas',
        'pandasai',
        'litellm',
        'matplotlib',
        'openpyxl'
    ]
    
    failed_imports = []
    
    for module in modules_to_check:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n❌ 以下模块导入失败: {', '.join(failed_imports)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有模块导入成功")
    return True

def check_config():
    """检查配置"""
    print("⚙️  检查配置...")
    
    # 检查.env文件
    if not Path(".env").exists():
        print("⚠️  .env文件不存在，将使用默认配置")
        return True
    
    # 检查关键配置
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv("QWEN_API_KEY")
    if not api_key:
        print("⚠️  QWEN_API_KEY未配置，某些功能可能无法使用")
    else:
        print("✅ API密钥已配置")
    
    return True

def check_file_structure():
    """检查文件结构"""
    print("📁 检查文件结构...")
    
    required_files = [
        "app.py",
        "config.py",
        "requirements.txt",
        "core/data_manager.py",
        "core/llm_manager.py",
        "core/analysis_engine.py",
        "utils/logger.py",
        "utils/error_handler.py"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"\n❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 文件结构完整")
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 智能数据分析平台 - 系统检查")
    print("=" * 60)
    
    checks = [
        ("文件结构", check_file_structure),
        ("模块导入", check_imports),
        ("配置检查", check_config),
        ("单元测试", run_tests)
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        print(f"\n{'-' * 30}")
        print(f"🔍 {check_name}")
        print(f"{'-' * 30}")
        
        try:
            if not check_func():
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name}检查失败: {e}")
            all_passed = False
    
    print(f"\n{'=' * 60}")
    if all_passed:
        print("🎉 所有检查通过！应用可以正常运行")
        print("\n启动应用:")
        print("  python run.py")
        print("  或")
        print("  streamlit run app.py")
    else:
        print("❌ 部分检查失败，请解决上述问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
